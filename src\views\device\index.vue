<template>
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form :inline="true" label-width="80px" :model="queryForm" size="mini">
          <el-form-item label="设备编号">
            <el-input v-model="queryForm.deviceNumber" :clearable="true" placeholder="请输入设备编号" />
          </el-form-item>
          <el-form-item label="所属商户">
            <el-select v-model="queryForm.merchantId" placeholder="请选择所属商户" style="width: 250px">
              <el-option label="全部" value="" />
              <el-option v-for="item in storeLists" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="运营地区">
            <el-cascader v-model="areaValue" :options="areaData" placeholder="请选择运营地区" :props="{
              value: 'areaCode',
              label: 'areaName',
              children: 'children',
            }" @change="onChangeArea" />
          </el-form-item>
          <el-form-item label="运营模式">
            <el-select v-model="queryForm.operationMode" placeholder="请选择运营模式">
              <el-option label="全部" value="" />
              <el-option v-for="item in operationModeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="网络状态">
            <el-select v-model="queryForm.networkStatus" placeholder="请选择网络状态">
              <el-option label="全部" value="" />
              <el-option v-for="item in networkList" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery">
              查 询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
      <vab-query-form-top-panel>
        <el-form :inline="true" label-width="80px" size="mini">
          <el-form-item label="快捷操作">
            <el-button icon="el-icon-circle-plus-outline" type="primary" @click="handleAdd">
              新增设备
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <!-- 按钮 -->
    </vab-query-form>
    <!-- 查询条件 -->
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" label="序号" width="100">
        <template #default="scope">
          {{
            (queryForm.currentPage - 1) * queryForm.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="设备信息" min-width="260">
        <template #default="{ row }">
          <div class="device-info">
            <p>设备编号：{{ row.id }}</p>
            <p>所属商户：{{ row.merchantName }}</p>
            <p>
              <span>运营模式：</span>
              <span>{{ row.operationModeName }}</span>
            </p>
            <p>
              运营地区：{{ row.provinceName }}{{ row.cityName
              }}{{ row.districtName }}{{ row.address }}
            </p>
            <p v-if="row.tags">
              <span>标签：</span>
              <el-tag v-for="(item, index) in stringToArray(row.tags)" :key="index" size="mini" :type="randomType()">
                {{ item }}
              </el-tag>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="在售餐品信息" min-width="190">
        <template #default="{ row }">
          <div v-if="row.dishList" class="food-info">
            <p>餐品名称：{{ row.dishList[0].dishName }}</p>
            <p>餐品价格：￥{{ row.dishList[0].price }}</p>
            <p>
              打包袋价格：￥{{ row.packagingPrice }}
            </p>
            <p>
              打包袋库存：{{ row.packagingInventory }}
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="设备网络信息" min-width="180">
        <template #default="{ row }">
          <div class="network-info">
            <p>
              <span>网络类型：</span>
              <span v-if="row.netWorkType == '1'">LAN</span>
              <span v-if="row.netWorkType == '2'">Wi-Fi</span>
              <span v-if="row.netWorkType == '8'">4G</span>
            </p>
            <p>
              <span>网络状态：</span>
              <el-tag v-if="row.networkStatus == 'online'" size="mini" type="success">
                在线
              </el-tag>
              <el-tag v-if="row.networkStatus == 'offline'" size="mini" type="warning">
                离线
              </el-tag>
            </p>
            <p v-if="row.netWorkType == '2'">
              <span>信号强度：</span>
              <span v-if="row.signalStrength < 40">
                信号异常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength > 40 && row.signalStrength < 71">
                正常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength == 99">无信号</span>
            </p>
            <p v-if="row.netWorkType == '8'">
              <span>信号强度：</span>
              <span v-if="row.signalStrength < 10">
                信号异常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength > 9 && row.signalStrength < 32">
                正常（{{ row.signalStrength }}）
              </span>
              <span v-if="row.signalStrength == 99">无信号</span>
            </p>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="故障">
        <template #default="{ row }">
          <p @click="openFaultDetails(row)" style="cursor: pointer; color: #409EFF"
            v-if="row.faultCount && row.faultCount > 0">有&nbsp;<span style="color: #F56C6C">{{ row.faultCount
            }}</span>&nbsp;个故障</p>

          <p v-else style="color: #909399">无</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="启用状态">
        <template #default="{ row }">
          <el-switch v-model="row.enabled" @change="changeSwitch(row)" />
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="150">
        <template #default="{ row }">
          <div>
            <el-link type="primary" @click="handleSelect(row)">查看</el-link>
          </div>
          <div>
            <el-link type="primary" @click="handleEdit(row)">编辑</el-link>
          </div>
          <div>
            <el-link type="primary" @click="handleCode(row)">二维码</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="queryForm.currentPage" :layout="layout" :page-size="queryForm.pageSize"
      :total="queryForm.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    <!-- end表格 -->
    <el-dialog :title="`设备ID：${qrcodeId} 二维码`" :visible.sync="centerDialogVisible" width="30%" center>
      <div class="qrcode-cont">
        <el-image style="width: 300px; height: 300px" :src="qrcodeUrl" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="centerDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="downloadBase64Image(qrcodeUrl, `设备ID${qrcodeId}二维码`)">下 载</el-button>
      </span>
    </el-dialog>

    <!-- 故障详情弹窗 -->
    <el-dialog title="故障详情" :visible.sync="faultDialogVisible" width="50%" center>
      <el-table v-loading="faultLoading" border :data="faultList">
        <div slot="empty" style="margin: 50px 0">
          <img class="img"
            src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
        </div>
        <el-table-column align="center" label="序号" width="120">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="故障类型" prop="name" />
        <el-table-column align="center" label="故障时间" prop="faultTime" />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="faultDialogVisible = false">关 闭</el-button>
      </span>
    </el-dialog>

    <!-- 子组件 -->
    <table-select ref="select" />
  </div>
</template>

<script>
import { deviceList, getAllMerchantIdsAndNames, setDeviceStatus, getDeviceQrCode, faultRecordList } from '@/api/device'
import { areaList } from '@/api/area'
import TableSelect from './components/TableSelect.vue'
export default {
  name: 'DeviceList',
  components: {
    TableSelect,
  },
  data() {
    return {
      centerDialogVisible: false, // 二维码
      qrcodeUrl: '', // 二维码内容
      qrcodeId: '', // 二维码ID
      faultDialogVisible: false, // 故障详情弹窗
      faultLoading: false, // 故障列表加载状态
      faultList: [], // 故障列表数据
      currentDeviceId: '', // 当前查看的设备ID
      storeLists: [], // 所有商户列表
      operationModeList: [
        { id: 1, name: '直营' },
        { id: 2, name: '联合运营' },
      ], // 运营模式、
      networkList: ['4G', 'Wi-Fi'],
      areaData: [], // 省市区
      areaValue: [''], // 省市区值
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 10, // 页数
        operType: 0, // 0查询，1导出
        total: 0, // 总条数
        deviceNumber: '', // 设备编号
        merchantId: '', // 所属商户ID
        province: '', // 省
        city: '', // 市
        district: '', // 区
        operationMode: '', // 运营模式
        networkStatus: '', // 网络状态
      },
      loading: false,
      tableData: [],
      layout: 'total, sizes, prev, pager, next, jumper',
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.$nextTick(() => {
      // 获取所有商户
      this.storeallLists()
      // 获取地区
      this.getAreaList()
      // 获取设备列表
      this.fetchData()
    })
  },
  methods: {
    // 打开故障详情
    async openFaultDetails(row) {
      this.currentDeviceId = row.id
      this.faultDialogVisible = true
      this.faultLoading = true

      try {
        const { code, data } = await faultRecordList({
          currentPage: 1,
          pageSize: 80,
          operType: 0,
          deviceId: Number(row.id)
        })

        if (code === '00000') {
          this.faultList = data.data || []
        } else {
          this.$message.error('获取故障列表失败')
          this.faultList = []
        }
      } catch (error) {
        console.error('获取故障列表失败:', error)
        this.$message.error('获取故障列表失败')
        this.faultList = []
      } finally {
        this.faultLoading = false
      }
    },
    // 开关
    async changeSwitch(e) {
      const { data } = await setDeviceStatus({
        id: Number(e.id),
        enabled: e.enabled ? 1 : 0,
      })
      if (data) {
        this.$message.success('操作成功')
      } else {
        this.$message.error('操作失败')
      }
    },
    // 字符串转数组
    stringToArray(e) {
      return e.split(',')
    },
    // 随机type
    randomType() {
      let fruits = ['primary', 'success', 'warning', 'danger', 'info']
      let randomFruit = fruits[Math.floor(Math.random() * fruits.length)]
      return randomFruit
    },
    // 获取省市区数据
    async getAreaList() {
      // 尝试从缓存获取数据
      const cacheKey = 'areaListData';
      const expireKey = 'areaListExpire';

      try {
        // 获取缓存数据
        const cachedData = localStorage.getItem(cacheKey);
        const expireTime = localStorage.getItem(expireKey);

        // 如果缓存存在且未过期，直接使用缓存数据
        if (cachedData && expireTime && Date.now() < parseInt(expireTime)) {
          this.areaData = [{ areaCode: '', areaName: '全部' }, ...JSON.parse(cachedData)];
          return;
        }

        // 如果缓存不存在或已过期，请求新数据
        const { data } = await areaList({});

        // 更新缓存，设置7天过期时间
        localStorage.setItem(cacheKey, JSON.stringify(data));
        localStorage.setItem(expireKey, String(Date.now() + (7 * 24 * 60 * 60 * 1000)));

        this.areaData = [{ areaCode: '', areaName: '全部' }, ...data];
      } catch (error) {
        console.error('获取或缓存省市区数据失败:', error);
        // 出错时尝试从API获取数据
        const { data } = await areaList({});
        this.areaData = [{ areaCode: '', areaName: '全部' }, ...data];
      }
    },
    // 省市区事件
    onChangeArea(e) {
      if (e.length > 0) {
        this.queryForm.province = e[0]
        this.queryForm.city = e[1]
        this.queryForm.district = e[2]
      }
    },
    //查询
    handleQuery() {
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    // 分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    //列表查询
    async fetchData() {
      this.loading = true
      const { data } = await deviceList(this.queryForm)
      this.tableData = data.data
      this.queryForm.total = data.paginator.totalRecord
      this.loading = false
    },
    // 所有商户下拉列表
    async storeallLists() {
      let { code, data } = await getAllMerchantIdsAndNames({})
      if (code == '00000') {
        this.storeLists = data
      }
    },
    // 添加设备
    handleAdd() {
      this.$router.push({
        path: '/device/add',
        query: {},
      })
    },
    // 编辑设备
    handleEdit(row) {
      this.$router.push({
        path: '/device/edit?id=' + row.id,
      })
    },
    // 二维码
    async handleCode(row) {
      const { data } = await getDeviceQrCode({
        code: `${row.id}_${row.district}`,
      })
      this.qrcodeId = row.id
      this.qrcodeUrl = `data:image/png;base64,${data}`
      this.centerDialogVisible = true
    },
    // 下载图片
    downloadBase64Image(base64Data, fileName) {
      // 1. 将 base64 数据转换为 Blob 对象
      const byteCharacters = atob(base64Data.split(",")[1]); // 去掉前缀（如 "data:image/png;base64,"）
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: "image/png" }); // 根据图片类型设置 MIME 类型

      // 2. 创建下载链接
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName || "image.png"; // 设置下载文件名

      // 3. 触发下载
      document.body.appendChild(link);
      link.click();

      // 4. 清理资源
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      this.centerDialogVisible = false
    },
    // 查看设备
    handleSelect(row) {
      // 显示设备详情信息
      this.$refs['select'].show(row)
    },
  },
}
</script>

<style lang="scss" scoped>
.link-padding {
  margin-left: 15px;
}

.table-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.table-empty {
  margin: 50px 0;
}

.table-column-price {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.device-info {
  min-width: 240px;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
    text-align: left;
  }
}

.qrcode-cont {
  display: flex;
  align-items: center;
  justify-content: center;
}

.network-info {
  min-width: 160px;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
    text-align: left;
  }
}

.food-info {
  min-width: 170px;
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
    text-align: left;
  }
}
</style>
