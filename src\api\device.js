import request from "@/utils/request";

/**
 * 设备列表
 * @param {*} data
 * @returns
 */
export async function deviceList(data) {
  return request({
    url: "/device/pageList",
    method: "post",
    data,
  });
}

/**
 * 设备列表
 * @param {*} data
 * @returns
 */
export async function setDeviceStatus(data) {
  return request({
    url: `/device/update/status`,
    method: "post",
    data,
  });
}

/**
 * 获取所有商户的ID和名称
 * @param {Object} data - 请求所需的数据对象
 * @returns {Promise} 返回一个包含所有商户ID和名称的Promise对象
 */
export async function getAllMerchantIdsAndNames(data) {
  return request({
    url: "/merchant-info/getAllMerchantIdsAndNames",
    method: "post",
    data,
  });
}

// 所有菜品名称
export async function allFoodList(data) {
  return request({
    url: "/dish/pageList",
    method: "post",
    data,
  });
}

// 新增设备
export async function addDevice(data) {
  return request({
    url: "/device/save",
    method: "post",
    data,
  });
}

// 设备详情
export async function deviceDetails(data) {
  return request({
    url: "/device/get",
    method: "post",
    data,
  });
}

// 修改设备
export async function editDetails(data) {
  return request({
    url: "/device/update",
    method: "post",
    data,
  });
}

// 设备故障列表
export async function faultList(data) {
  return request({
    url: "/fault-type/pageList",
    method: "post",
    data,
  });
}

// 设备故障记录列表
export async function faultRecordList(data) {
  return request({
    url: "/fault-record/pageList",
    method: "post",
    data,
  });
}

// 生成设备访问二维码
export async function getDeviceQrCode(data) {
  return request({
    url: "/app/device/getDeviceQrCode",
    method: "post",
    data,
  });
}
