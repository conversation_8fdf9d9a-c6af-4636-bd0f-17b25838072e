<template>
  <!-- 查看详情 -->
  <div class="index-container">
    <el-drawer :direction="direction" :size="1300" :visible.sync="drawer">
      <div class="drawer-container">
        <div class="top-container">
          <el-image
            :src="info.imgUrl"
            style="width: 23%; height: 300px"
          />
          <el-descriptions
            border
            :column="4"
            direction="vertical"
            style="width: 75%"
          >
            <el-descriptions-item label="设备编号">
              {{ info.id }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ timestampToDate(info.createTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="所属商户">
              {{ info.merchantName }}
            </el-descriptions-item>
            <el-descriptions-item label="DTU">
              <span v-if="info.dtuId">{{ info.dtuId }}</span>
              <span v-else>暂未绑定</span>
            </el-descriptions-item>
            <el-descriptions-item label="SIM卡">
              <span v-if="info.simCard">{{ info.simCard }}</span>
              <span v-else>暂未绑定</span>
            </el-descriptions-item>
            <el-descriptions-item label="标签">
              <span v-if="info.tags">{{ info.tags }}</span>
              <span v-else>暂无标签</span>
            </el-descriptions-item>
            <el-descriptions-item label="运营地区">
              {{ info.provinceName }}{{ info.cityName}}{{ info.districtName }}{{ info.address }}
            </el-descriptions-item>
            <el-descriptions-item label="运营模式">
              <span>{{ info.operationModeName }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="在线状态">
              <el-tag
                v-if="info.networkStatus == 'online'"
                size="mini"
                type="success"
              >
                在线
              </el-tag>
              <el-tag
                v-if="info.networkStatus == 'offline'"
                size="mini"
                type="warning"
              >
                离线
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="网络类型">
              <span v-if="info.netWorkType == '1'">LAN</span>
              <span v-if="info.netWorkType == '2'">Wi-Fi</span>
              <span v-if="info.netWorkType == '8'">4G</span>
            </el-descriptions-item>
            <el-descriptions-item label="信号强度">
              <p v-if="info.netWorkType == '2'">
                <span v-if="info.signalStrength < 40">
                  信号异常（{{ info.signalStrength }}）
                </span>
                <span v-if="info.signalStrength > 40 && info.signalStrength < 71">
                  正常（{{ info.signalStrength }}）
                </span>
                <span v-if="info.signalStrength == 99">无信号</span>
              </p>
              <p v-if="info.netWorkType == '8'">
                <span v-if="info.signalStrength < 10">
                  信号异常（{{ info.signalStrength }}）
                </span>
                <span v-if="info.signalStrength > 9 && info.signalStrength < 32">
                  正常（{{ info.signalStrength }}）
                </span>
                <span v-if="info.signalStrength == 99">无信号</span>
              </p>
            </el-descriptions-item>
            <el-descriptions-item label="分账模板">
              <span v-if="info.splitTemplateName">
                {{ info.splitTemplateName }}
              </span>
              <span v-else>暂无分账模板</span>
            </el-descriptions-item>
            <el-descriptions-item label="在售餐品" v-if="info.dishList">
              {{ info.dishList[0].dishName }}
            </el-descriptions-item>
            <el-descriptions-item label="餐品价格" v-if="info.dishList">
              ¥{{ info.dishList[0].price }}
            </el-descriptions-item>
            <el-descriptions-item label="投放地址">
              {{ info.address }}
            </el-descriptions-item>
            <el-descriptions-item label="打包袋价格">
              {{ info.packagingPrice }}
            </el-descriptions-item>
            <el-descriptions-item label="打包袋库存">
              {{ info.packagingInventory }}
            </el-descriptions-item>
            <el-descriptions-item label="启用状态">
              <el-switch v-model="info.isEnabled" disabled />
            </el-descriptions-item>
            <el-descriptions-item label="故障">
              <div>
                <p v-if="info.fault" style="color: #909399">无</p>
                <p v-else style="cursor: pointer; color: #F56C6C" @click="openFaultDetails(info)">
                  有
                </p>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="centre-container">
          <h3>餐品信息</h3>
          <div class="dish-list">
            <div
                  v-for="(item, index) in info.dishList"
                  :key="index"
                  class="dish-li"
                >
                  <p class="li-code">餐品编号：{{ item.dishId }}</p>
                  <el-image class="li-image" :src="item.imageUrl" />
                  <p class="li-name">{{ item.dishName }}</p>
                  <p class="li-code">价格：￥{{ item.price }}</p>
                  <p class="li-code">库存：{{ item.stock }}</p>
                  <p class="li-code">预警库存：{{ item.warnStock }}</p>
                </div>
          </div>
        </div>
        <div class="bottom-container">
          <h3>数据统计</h3>
          <div class="statistics-list">
            <div class="statistics-li">
              <span class="value">{{ info.todayOrderNum }}</span>
              <span class="label">今日订单</span>
            </div>
            <div class="statistics-li">
              <span class="value">{{ info.todayOrderAmount }}</span>
              <span class="label">今日收益</span>
            </div>
            <div class="statistics-li">
              <span class="value">{{ info.totalOrderNum }}</span>
              <span class="label">累计订单</span>
            </div>
            <div class="statistics-li">
              <span class="value">{{ info.totalOrderAmount }}</span>
              <span class="label">累计收益</span>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import { deviceDetails } from '@/api/device'
  export default {
    name: 'TableSelect',
    components: {},
    data() {
      return {
        drawer: false, // 抽屉显示状态
        direction: 'ltr', // 抽屉方向
        labelPosition: 'right', // 表单label对齐方式
        info: {}, // 设备详情
      }
    },
    methods: {
      // 时间戳转日期
      timestampToDate(timestamp) {
        // 创建一个新的 Date 对象
        let date = new Date(timestamp);
        // 获取年份、月份（注意：月份是从0开始的）、日期
        let year = date.getFullYear();
        let month = String(date.getMonth() + 1).padStart(2, '0'); // 转换为1-12的月份
        let day = String(date.getDate()).padStart(2, '0');
        // 获取小时、分钟、秒数
        let hours = String(date.getHours()).padStart(2, '0');
        let minutes = String(date.getMinutes()).padStart(2, '0');
        let seconds = String(date.getSeconds()).padStart(2, '0');
        // 返回格式化的字符串
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      show(row) {
        // 获取设备详情
        this.getDeviceDetails(Number(row.id))
      },
      // 获取设备详情
      async getDeviceDetails(id) {
        const { code, data } = await deviceDetails({ id })
        if (code == '00000') {
          this.info = data
          this.drawer = true
        }
      },
      close() {
        this.drawer = false
      },
      handleClose(done) {
        done()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .index-container {
    width: 100%;
    .drawer-container {
      width: 100%;
      padding: 0 30px;
      display: flex;
      flex-direction: column;
      .top-container {
        width: 100%;
        display: flex;
        justify-content: space-between;
      }
      .centre-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding-top: 30px;
        border-top: 2px solid #dcdfe6;
        margin-top: 30px;
        .dish-list {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          .dish-li {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px 15px 0;
            border-radius: 5px;
            margin: 10px;
            position: relative;
            background-color: #F4F4F5;
            box-shadow: 4px 4px 10px 0 rgba(163,178,202,.2);
            .li-code {
              font-size: 14px;
              margin: 4px 0;
            }
            .li-image {
              width: 200px;
              height: 200px;
              margin-top: 5px;
            }
            .li-name {
              font-size: 14px;
            }
          }
        }
      }
      .bottom-container {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding-top: 30px;
        border-top: 2px solid #dcdfe6;
        margin-top: 30px;
        .statistics-list {
          width: 100%;
          display: flex;
          align-content: center;
          margin-top: 20px;
          .statistics-li {
            width: 20%;
            display: flex;
            flex-direction: column;
            align-items: center;
            .value {
              font-size: 30px;
              font-weight: 600;
              color: #333333;
            }
            .label {
              font-size: 14px;
              color: #999999;
              margin-top: 14px;
            }
          }
        }
      }
    }
  }
</style>
