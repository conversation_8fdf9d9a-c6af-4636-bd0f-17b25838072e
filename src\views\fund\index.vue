<template>
  <div class="user-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="78px" :model="queryForm">
          <el-form-item label="支付日期">
            <el-date-picker v-model="queryForm.payTime" end-placeholder="结束日期" range-separator="至"
              start-placeholder="开始日期" type="daterange" @change="onDateRange" />
          </el-form-item>
          <el-form-item label="订单编号">
            <el-input v-model="queryForm.orderId" :clearable="true" placeholder="请输入订单编号" />
          </el-form-item>
          <el-form-item label="所属商户">
            <el-select v-model="queryForm.merchantId" placeholder="请选择所属商户" style="width: 250px">
              <el-option label="全部" value="" />
              <el-option v-for="item in storeLists" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="handleQuery">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-bottom-panel>
        <div class="money-list">
          <div class="money-li">
            <p class="label">今日订单</p>
            <p class="value">{{ count.todayPendingOrderCount }}</p>
          </div>
          <div class="money-li">
            <p class="label">今日收益（当日收益于次日12点前结算到账）</p>
            <p class="value">¥{{ count.todayPendingOrderAmount }}</p>
          </div>
          <!-- <div class="money-li">
            <p class="label">今日待结算订单</p>
            <p class="value">{{ count.todayPendingOrderCount }}</p>
          </div>
          <div class="money-li">
            <p class="label">今日待结算金额</p>
            <p class="value">¥{{ count.todayPendingOrderCount }}</p>
          </div>
          <div class="money-li">
            <p class="label">昨日待结算订单</p>
            <p class="value">{{ count.yesterdayTotalOrderCount }}</p>
          </div>
          <div class="money-li">
            <p class="label">昨日已结算订单</p>
            <p class="value">{{ count.yesterdaySettledOrderCount }}</p>
          </div>
          <div class="money-li">
            <p class="label">昨日应结算金额</p>
            <p class="value">¥{{ count.yesterdayTotalOrderAmount }}</p>
          </div>
          <div class="money-li">
            <p class="label">昨日已结算金额</p>
            <p class="value">¥{{ count.yesterdaySettledOrderAmount }}</p>
          </div> -->
        </div>
      </vab-query-form-bottom-panel>
    </vab-query-form>
    <!-- 查询条件 -->
    <!-- begin表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row }">
          <span>{{ row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="订单编号" width="160">
        <template #default="{ row }">
          <span>{{ row.orderId }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="订单金额" width="130">
        <template #default="{ row }">
          <p>订单总额：¥{{ row.totalPayment }}</p>
          <p>实付金额：¥{{ row.actualPayment }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="订单状态" width="100">
        <template #default="{ row }">
          <el-tag v-if="row.orderStatus == '0'" type="primary">
            待支付
          </el-tag>
          <el-tag v-if="row.orderStatus == '1'" type="success">
            已支付
          </el-tag>
          <el-tag v-if="row.orderStatus == '2'" type="success">
            已分账
          </el-tag>
          <el-tag v-if="row.orderStatus == '3'" type="warning">
            分账失败
          </el-tag>
          <el-tag v-if="row.orderStatus == '4'" type="danger">
            退款中
          </el-tag>
          <el-tag v-if="row.orderStatus == '5'" type="success">
            已退款
          </el-tag>
          <el-tag v-if="row.orderStatus == '6'" type="warning">
            退款失败
          </el-tag>
          <el-tag v-if="row.orderStatus == '7'" type="info">
            已取消
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否分账" width="80">
        <template #default="{ row }">
          <el-tag v-if="row.ifSplitAccount == 1" type="success">是</el-tag>
          <el-tag v-else type="warning">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="分账方" min-width="150">
        <template #default="{ row }">
          <p>{{ row.merchantName }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="分账比例" min-width="100">
        <template #default="{ row }">
          <p>{{ row.ratio }}%</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="分账金额" min-width="100">
        <template #default="{ row }">
          <p>¥{{ row.splitAmount }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="手续费" min-width="100">
        <template #default="{ row }">
          <p>¥{{ row.charge }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否退款" min-width="100">
        <template #default="{ row }">
          <el-tag type="warning" v-if="row.ifRefund == 1">是</el-tag>
          <el-tag type="success" v-else>否</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="支付、分账、提现到账时间" width="210">
        <template #default="{ row }">
          <div class="food-info">
            <p>支付时间：{{ row.payTime }}</p>
            <p>分账时间：{{ row.splitTime }}</p>
            <p>提现到账：{{ row.withdrawTime }}</p>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination v-if="queryForm.total > 20" background :current-page="queryForm.currentPage" :layout="layout"
      :page-size="queryForm.pageSize" :total="queryForm.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
    <!-- end表格 -->
  </div>
</template>

<script>
import { pageList, statistics } from '@/api/fund'
import { getAllMerchantIdsAndNames } from '@/api/device'
export default {
  name: 'FundIndex',
  data() {
    return {
      storeLists: [], // 所有商户列表
      orderDetailsVisible: false, // 弹窗状态
      orderDetailsLoading: false, // 加载状态
      payTime: '', // 支付时间
      count: { // 统计数据
        'todayPendingOrderCount': 0,
        'todayPendingOrderAmount': 0,
        'yesterdayTotalOrderCount': 0,
        'yesterdaySettledOrderCount': 0,
        'yesterdayTotalOrderAmount': 0,
        'yesterdaySettledOrderAmount': 0
      },
      queryForm: {
        currentPage: 1, // 页码
        pageSize: 20, // 页数
        total: 0, // 总页数
        operType: 0, // 0查询，1 导出
        payStartTime: '', // 支付开始时间
        payEndTime: '', // 支付结束时间
        orderId: '', // 订单编号
        merchantId: '', // 所属商户
      },
      loading: false, // 加载状态
      tableData: [], // 表格数据
      layout: 'total, sizes, prev, pager, next, jumper', // 分页布局
      orderInfo: {}, // 订单详情
    }
  },
  computed: {},
  beforeMount() { },
  beforeDestroy() { },
  created() {
    this.$nextTick(() => {
      // 获取统计数据
      this.getStatistics()
      // 获取所有商户列表
      this.storeallLists()
      // 获取列表
      this.fetchData()
    })
  },
  methods: {
    // 选择日期
    onDateRange(e) {
      this.queryForm.payStartTime = this.formatDateToString(e[0])
      this.queryForm.payEndTime = this.formatDateToString(e[1])
      // 查询
      this.handleQuery()
    },
    // 获取统计数据
    async getStatistics() {
      const { code, data } = await statistics({})
      if (code == '00000') {
        this.count = data
      }
    },
    // 所有商户下拉列表
    async storeallLists() {
      let { code, data } = await getAllMerchantIdsAndNames({})
      if (code == '00000') {
        this.storeLists = data
      }
    },
    // 选择结算状态
    onSettleStatus() {
      // 查询
      this.handleQuery()
    },
    // 日期转换成字符串
    formatDateToString(dateString) {
      // 创建一个新的 Date 对象，传入你的日期字符串
      const date = new Date(dateString)
      // 检查是否创建了有效的日期对象
      if (isNaN(date.getTime())) {
        throw new Error('Invalid date string')
      }
      // 提取年份、月份（注意：getMonth() 返回的月份是从0开始的，所以需要加1）、日期
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // 确保两位数显示
      const day = String(date.getDate()).padStart(2, '0') // 确保两位数显示

      // 拼接成你想要的格式并返回
      return `${year}-${month}-${day}`
    },
    //查询
    handleQuery() {
      this.tableData = []
      this.queryForm.currentPage = 1
      this.fetchData()
    },
    //分页
    handleSizeChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    //分页
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },
    // 日期范围
    dateRange(e) {
      if (e) {
        this.queryForm.startDate = e[0] // 起始日期：yyyy-MM-dd
        this.queryForm.stopDate = e[1] // 截止日期：yyyy-MM-dd
      } else {
        this.queryForm.startDate = '' // 起始日期：yyyy-MM-dd
        this.queryForm.stopDate = '' // 截止日期：yyyy-MM-dd
      }
    },
    // 列表查询 查询
    async fetchData() {
      let that = this
      that.loading = true
      const { data } = await pageList(this.queryForm)
      that.tableData = data.data
      that.queryForm.total = data.paginator.totalRecord
      that.loading = false
    },
    // 更新列表查询
    async updateFetchData() {
      let that = this
      that.loading = true
      const { data } = await pageList(this.queryForm)
      that.tableData = data.data
      that.queryForm.total = data.paginator.totalRecord
      that.loading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.money-list {
  width: 100%;
  display: flex;
  align-content: center;
  // justify-content: space-around;
  margin: 20px 0 20px;

  .money-li {
    width: 20.6%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .label {
      font-size: 14px;
      color: #606266;
      margin-bottom: 12px;
    }

    .value {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.link-padding {
  margin-left: 15px;
}

.form-style {
  display: flex;

  .form-item {
    width: 50%;
  }
}

.input-tips {
  font-size: 13px;
  color: #f56c6c;
}

.input-refundForm {
  font-size: 14px;
  color: #409eff;
}

.order-details {
  width: 80%;
  margin: 20px auto;
}

p {
  margin-bottom: 0;
  margin-top: 5px;
}

.food-info {
  display: flex;
  flex-direction: column;

  p {
    margin: 0;
    text-align: left;
  }
}
</style>
