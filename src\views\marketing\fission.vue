<template>
  <div class="index-container">
    <!-- 查询条件 -->
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form :inline="true" size="mini">
          <el-form-item label="操作">
            <el-button type="primary" @click="handleAddCoupon">新增裂变券</el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>

    <!-- 表格 -->
    <el-table ref="tableSort" v-loading="loading" border :data="tableData">
      <div slot="empty" style="margin: 50px 0">
        <img class="img" src="https://longyi-oss-tongyong.oss-cn-beijing.aliyuncs.com/image/1865665326720536578.png" />
      </div>
      <el-table-column align="center" prop="id" label="裂变券ID" min-width="80" />
      <el-table-column align="center" prop="name" label="裂变券名称" min-width="120" />
      <el-table-column align="center" prop="type" label="裂变券类型" min-width="120">
        <template #default="{ row }">
          {{ row.couponTypeName }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="createTime" label="创建时间" min-width="120">
        <template #default="{ row }">
          {{ row.createTime }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="total" label="发放数量" min-width="100">
        <template #default="{ row }">
          {{ row.totalQuota }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="used" label="使用数量" min-width="100">
        <template #default="{ row }">
          {{ row.usedCount }}
        </template>
      </el-table-column>

      <el-table-column align="center" prop="usageRate" label="使用率" min-width="100">
        <template #default="{ row }">
          {{ row.useRate }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <div class="operate-style">
            <p>
              <el-link type="primary" icon="el-icon-view" @click="handleView(row)">查看</el-link>
            </p>
            <p v-if="row.published == 0">
              <el-link type="warning" icon="el-icon-edit" @click="handleEdit(row)">编辑</el-link>
            </p>
            <p v-if="row.published == 0 && row.publishType == 0">
              <el-link type="success" icon="el-icon-thumb" @click="handleRelease(row)">发放</el-link>
            </p>
            <p v-if="row.published == 0 && row.publishType == 1">
              <el-link type="danger" icon="el-icon-switch-button" @click="handleCancel(row)">取消</el-link>
            </p>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination v-if="queryForm.total > 0" background :current-page="queryForm.currentPage" :layout="layout"
      :page-size="queryForm.pageSize" :total="queryForm.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />

    <!-- 查看裂变券详情弹窗 -->
    <el-dialog title="裂变券详情" :visible.sync="viewDialogVisible" width="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="裂变券ID">{{ currentCoupon.id }}</el-descriptions-item>
        <el-descriptions-item label="裂变券名称">{{ currentCoupon.name }}</el-descriptions-item>
        <el-descriptions-item label="裂变券类型">{{ currentCoupon.couponTypeName }}</el-descriptions-item>
        <el-descriptions-item label="领取条件">{{ currentCoupon.receiveConditionName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ currentCoupon.createTime }}</el-descriptions-item>
        <el-descriptions-item label="发放时间">
          <div v-if="currentCoupon.published == 0">
            <el-tag type="warning" v-if="currentCoupon.publishType == 0">未发布</el-tag>
            <div v-else>{{ currentCoupon.scheduledPublishTime }}&nbsp;&nbsp;<el-tag
                v-if="currentCoupon.publishType == 1">定时</el-tag></div>
          </div>
          <div v-else>
            <div v-if="currentCoupon.publishType == 0">{{ currentCoupon.publishTime }}</div>
            <div v-else>{{ currentCoupon.scheduledPublishTime }}&nbsp;&nbsp;<el-tag
                v-if="currentCoupon.publishType == 1">定时</el-tag></div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="发放数量">{{ currentCoupon.totalQuota }}</el-descriptions-item>
        <el-descriptions-item label="领取数量">{{ currentCoupon.collectCount }}</el-descriptions-item>
        <el-descriptions-item label="使用数量">{{ currentCoupon.usedCount }}</el-descriptions-item>
        <el-descriptions-item label="使用率">{{ currentCoupon.useRate }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="currentCoupon.published == '0'" type="warning">未发放</el-tag>
          <el-tag v-if="currentCoupon.published == '1'" type="success">已发放</el-tag>
        </el-descriptions-item>
      </el-descriptions>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 编辑/新增裂变券弹窗 -->
    <el-drawer :title="dialogTitle" :visible.sync="editDialogVisible" direction="rtl" size="50%"
      :before-close="handleDrawerClose">
      <div style="padding: 0 20px;">
        <el-form :model="couponForm" :rules="rules" label-position="top" ref="couponForm">
          <el-form-item label="裂变券名称" prop="name">
            <el-input v-model="couponForm.name" placeholder="请输入裂变券名称" :maxlength="20" />
          </el-form-item>

          <el-form-item label="裂变券类型" prop="couponType">
            <el-select v-model="couponForm.couponType" placeholder="请选择裂变券类型" style="width: 100%">
              <el-option label="金额券" :value="0" />
              <el-option label="折扣券" :value="1" />
            </el-select>
          </el-form-item>

          <el-form-item label="优惠金额" prop="discountValue" v-if="couponForm.couponType === 0">
            <el-input v-model.number="couponForm.discountValue" placeholder="请输入优惠金额" type="number" />
          </el-form-item>
          <el-form-item label="折扣比例" prop="discountValue" v-if="couponForm.couponType === 1">
            <el-input v-model.number="couponForm.discountValue" placeholder="例：85（表示85折）" type="number" />
          </el-form-item>

          <el-form-item label="最低消费金额" prop="minConsumption" v-if="[0, 1].includes(couponForm.couponType)">
            <el-input v-model.number="couponForm.minConsumption" placeholder="请输入最低消费金额，例: 100" type="number" />
            <span style="color: #999; font-size: 12px">不填表示无使用门槛</span>
          </el-form-item>

          <el-form-item label="使用范围" prop="useScope">
            <el-select v-model="couponForm.useScope" placeholder="请选择使用设备" style="width: 100%"
              @change="handleUseScopeChange">
              <el-option label="所有设备" :value="0" />
              <el-option label="指定设备" :value="1" />
            </el-select>
            <div v-if="couponForm.useScope === 1" style="margin-top: 10px;">
              <el-button type="text" @click="showDeviceDialog">选择设备</el-button>
              <span v-if="selectedDevices.length > 0" style="margin-left: 10px; color: #666;">
                已选择 {{ selectedDevices.length }} 个设备
              </span>
            </div>
          </el-form-item>
          <el-form-item label="优惠券有效期类型" prop="validPeriodType">
            <el-select v-model="couponForm.validPeriodType" placeholder="请选择有效期类型" style="width: 100%">
              <el-option label="当日使用" :value="0" />
              <el-option label="领取后N日" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="有效天数" prop="validDays" v-if="couponForm.validPeriodType === 1">
            <el-input v-model.number="couponForm.validDays" placeholder="请输入领取后有效天数" type="number" />
          </el-form-item>
          <el-form-item style="text-align: center; margin-top: 20px;">
            <el-button @click="editDialogVisible = false" style="width: 120px; margin-right: 20px;">取 消</el-button>
            <el-button type="primary" @click="submitForm" style="width: 120px;">{{ dialogTitle === '新增裂变券' ? '创建裂变券' :
              '更新裂变券' }}</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>

    <!-- 确认发放弹窗 -->
    <el-dialog title="确认发放" :visible.sync="releaseDialogVisible" width="30%">
      <p>确定要发放 "{{ currentCoupon.name }}" 裂变券吗？</p>
      <el-form :model="releaseForm" label-width="100px" style="margin-top: 20px;">
        <el-form-item label="发放方式">
          <el-radio-group v-model="releaseForm.publishType">
            <el-radio :label="1">立即发放</el-radio>
            <el-radio :label="2">定时发放</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发放时间" v-if="releaseForm.publishType === 2">
          <el-date-picker v-model="releaseForm.publishTime" type="datetime" placeholder="选择发放时间"
            value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" :picker-options="publishTimeOptions" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="releaseDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmRelease">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 确认取消弹窗 -->
    <el-dialog title="确认取消" :visible.sync="cancelDialogVisible" width="30%">
      <p>确定要取消 "{{ currentCoupon.name }}" 裂变券吗？</p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmCancel">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 设备选择弹窗 -->
    <el-dialog title="选择设备" :visible.sync="deviceDialogVisible" width="60%">
      <el-form :inline="true" :model="deviceQueryForm" class="demo-form-inline">
        <el-form-item label="设备名称">
          <el-input v-model="deviceQueryForm.deviceName" placeholder="请输入设备名称" clearable />
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input v-model="deviceQueryForm.deviceCode" placeholder="请输入设备编号" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleDeviceQuery">查询</el-button>
        </el-form-item>
      </el-form>

      <el-table ref="deviceTable" :data="deviceList" style="width: 100%" @selection-change="handleDeviceSelectionChange"
        v-loading="deviceLoading">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="设备ID" />
        <el-table-column prop="merchantName" label="设备商户" />
        <el-table-column prop="deviceCode" label="设备位置">
          <template #default="{ row }">
            {{ row.provinceName }}{{ row.cityName }}{{ row.districtName }}{{ row.address }}
          </template>
        </el-table-column>
        <el-table-column prop="" label="设备状态">
          <template #default="{ row }">
            <el-tag v-if="row.networkStatus == 'online'" size="mini" type="success">
              在线
            </el-tag>
            <el-tag v-if="row.networkStatus == 'offline'" size="mini" type="warning">
              离线
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container" style="margin-top: 15px; text-align: right;">
        <el-pagination background :current-page="deviceQueryForm.currentPage" :page-sizes="[10, 20, 30, 50]"
          :page-size="deviceQueryForm.pageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="deviceQueryForm.total" @size-change="handleDeviceSizeChange"
          @current-change="handleDevicePageChange" />
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="deviceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmDeviceSelection">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { couponList, createCoupon, getDeviceList, publishCoupons, cancelCoupons, couponDetails, updateCoupon } from '@/api/marketing'

export default {
  name: 'FissionManagement',
  components: {},
  data() {
    return {
      loading: false, // 加载状态
      viewDialogVisible: false, // 查看裂变券详情弹窗
      editDialogVisible: false, // 编辑/新增裂变券弹窗
      releaseDialogVisible: false, // 确认发放弹窗
      cancelDialogVisible: false, // 确认取消弹窗
      dialogTitle: '新增裂变券', // 弹窗标题
      currentCoupon: {}, // 当前裂变券
      queryForm: {
        currentPage: 1, // 当前页
        pageSize: 20, // 每页条数
        total: 0, // 总条数
        scene: 2, // 1普通优惠券，2裂变券
      },
      layout: 'total, sizes, prev, pager, next, jumper', // 分页布局
      tableData: [], // 表格数据
      couponForm: { // 裂变券表单
        id: null, // 裂变券ID，编辑时使用
        scene: 2, // 场景值：1-普通优惠券，2-裂变券
        name: '', // 裂变券名称
        couponType: null, // 裂变券类型 0:金额券 1:折扣券 2:无门槛券
        discountValue: null, // 优惠金额/折扣比例
        validPeriodType: 0, // 有效期类型：0-当日使用，1-领取后N天
        validDays: 1, // 领取后有效天数
        minConsumption: null, // 最低消费金额
        useScope: null, // 使用范围：0所有设备，1指定设备
        deviceIds: [], // 指定设备ID列表
      },
      rules: { // 表单验证规则
        name: [
          { required: true, message: '请输入裂变券名称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        couponType: [
          { required: true, message: '请选择裂变券类型', trigger: 'change' }
        ],
        discountValue: [
          { required: true, message: '请输入优惠金额', trigger: 'blur' }
        ],
        validPeriodType: [
          { required: true, message: '请选择有效期类型', trigger: 'change' }
        ],
        validDays: [
          { required: true, message: '请输入有效天数', trigger: 'blur' },
          { type: 'number', min: 1, message: '有效天数必须大于0', trigger: 'blur' }
        ],
        minConsumption: [
          { pattern: /^\d+$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ],
        useScope: [
          { required: true, message: '请选择使用范围', trigger: 'change' }
        ],
      },
      deviceDialogVisible: false, // 设备选择弹窗
      deviceList: [], // 设备列表
      selectedDevices: [], // 已选择的设备
      deviceLoading: false, // 设备列表加载状态
      deviceQueryForm: { // 设备查询表单
        currentPage: 1, // 当前页
        pageSize: 10, // 每页条数
        total: 0, // 总条数
        deviceName: '', // 设备名称
        deviceCode: '' // 设备编号
      },
      releaseForm: {
        templateId: null, // 裂变券模板ID
        publishType: 1, // 1: 立即发放, 2: 定时发放
        publishTime: '' // 定时发放时间
      },
      publishTimeOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 不能选择过去的日期
        }
      },
    }
  },
  created() {
    this.fetchData()
    // 监听裂变券类型变化，动态修改验证规则
    this.$watch('couponForm.couponType', (newVal) => {
      // 重置表单验证
      if (this.$refs.couponForm) {
        this.$refs.couponForm.clearValidate()
      }

      if (newVal === 1) { // 金额券
        this.rules.minConsumption = [
          { pattern: /^\d+$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ]
        this.rules.discountValue = [
          { required: true, message: '请输入优惠金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
        ]
      } else { // 折扣券
        // 折扣券不需要最低消费金额
        this.rules.minConsumption = []
        // 折扣券需要折扣比例
        this.rules.discountValue = [
          { required: true, message: '请输入折扣比例', trigger: 'blur' }
        ]
      }
    })

    // 监听有效期类型变化，动态修改验证规则
    this.$watch('couponForm.validPeriodType', (newVal) => {
      // 重置表单验证
      if (this.$refs.couponForm) {
        this.$refs.couponForm.clearValidate()
      }

      if (newVal === 1) { // 领取后N日
        this.rules.validDays = [
          { required: true, message: '请输入有效天数', trigger: 'blur' },
          { type: 'number', min: 1, message: '有效天数必须大于0', trigger: 'blur' }
        ]
      } else { // 当日使用
        this.rules.validDays = []
      }
    })
  },
  methods: {
    // 获取裂变券列表
    async fetchData() {
      this.loading = true
      try {
        const { code, data } = await couponList(this.queryForm)
        if (code === '00000') {
          this.tableData = data.data
          this.queryForm.total = data.paginator.totalRecord
        }
      } catch (error) {
        console.error('获取裂变券列表失败', error)
      } finally {
        this.loading = false
      }
    },

    // 分页处理
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.queryForm.currentPage = val
      this.fetchData()
    },

    // 查看裂变券详情
    async handleView(row) {
      try {
        const { code, data } = await couponDetails({ id: Number(row.id) })
        if (code === '00000') {
          this.currentCoupon = data
          this.viewDialogVisible = true
        } else {
          this.$message.error('获取裂变券详情失败')
        }
      } catch (error) {
        console.error('获取裂变券详情失败', error)
        this.$message.error('获取裂变券详情失败')
      }
    },

    // 新增裂变券
    handleAddCoupon() {
      this.dialogTitle = '新增裂变券'
      // 重置表单为默认值，与data中定义保持一致
      this.couponForm = {
        name: '', // 裂变券名称
        scene: 2, // 场景值：1-普通优惠券，2-裂变券
        couponType: null, // 裂变券类型 1:金额券 2:折扣券 3:无门槛券
        discountValue: null, // 优惠金额/折扣比例
        validPeriodType: 0, // 有效期类型：0-当日使用，1-领取后N天
        validDays: 1, // 领取后有效天数
        minConsumption: null, // 最低消费金额
        useScope: null, // 使用范围：0所有设备，1指定设备
        deviceIds: [], // 指定设备ID列表
      }
      this.selectedDevices = []
      this.editDialogVisible = true
    },

    // 编辑裂变券
    async handleEdit(row) {
      this.dialogTitle = '编辑裂变券'
      try {
        const { code, data } = await couponDetails({ id: Number(row.id) })
        if (code === '00000') {
          // 将返回的数据赋值给 couponForm
          this.couponForm = {
            id: data.id, // ID
            name: data.name, // 裂变券名称
            scene: data.scene, // 场景值：1-普通优惠券，2-裂变券
            couponType: data.couponType, // 裂变券类型
            discountValue: data.discountValue, // 优惠金额或折扣比例
            validPeriodType: data.validPeriodType || 0, // 有效期类型，默认为当日使用
            validDays: data.validDays || 1, // 领取后有效天数，默认为1天
            minConsumption: data.minConsumption || 0, // 最低消费金额，默认为0表示无门槛
            useScope: data.useScope, // 使用范围
            deviceIds: data.deviceIds || [], // 设备ID列表，默认为空数组
          }

          // 如果有设备列表，初始化已选择的设备
          if (data.deviceIds && data.deviceIds.length > 0 && data.deviceList) {
            this.selectedDevices = data.deviceList // 设置已选择的设备列表
          } else {
            this.selectedDevices = [] // 没有设备时设为空数组
          }

          this.currentCoupon = data // 保存当前编辑的裂变券完整数据
          this.editDialogVisible = true // 显示编辑弹窗
        } else {
          this.$message.error('获取裂变券详情失败')
        }
      } catch (error) {
        console.error('获取裂变券详情失败', error)
        this.$message.error('获取裂变券详情失败')
      }
    },

    // 发放裂变券
    handleRelease(row) {
      this.currentCoupon = { ...row }
      this.releaseForm.templateId = row.id
      this.releaseDialogVisible = true
    },

    // 取消裂变券
    handleCancel(row) {
      this.currentCoupon = { ...row }
      this.cancelDialogVisible = true
    },

    // 确认发放
    async confirmRelease() {
      if (this.releaseForm.publishType === 2 && !this.releaseForm.publishTime) {
        this.$message.warning("请选择发放时间");
        return
      }
      try {
        const { code } = await publishCoupons(this.releaseForm)
        if (code === '00000') {
          this.$message.success('裂变券发放成功')
          this.fetchData()
        }
      } catch (error) {
        this.$message.error('裂变券发放失败')
      } finally {
        this.releaseDialogVisible = false
        this.releaseForm = {
          publishType: 1,
          publishTime: ''
        }
      }
    },

    // 确认取消
    async confirmCancel() {
      try {
        const { code } = await cancelCoupons({ templateId: Number(this.currentCoupon.id) })
        if (code === '00000') {
          this.$message.success('裂变券已取消')
          this.fetchData()
        }
      } catch (error) {
        this.$message.error('取消裂变券失败')
      } finally {
        this.cancelDialogVisible = false
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.couponForm.validate(async valid => {
        if (valid) {
          // 确保数字字段是数字类型
          this.couponForm.discountValue = parseFloat(this.couponForm.discountValue);
          if (this.couponForm.minConsumption !== null && this.couponForm.minConsumption !== '') {
            this.couponForm.minConsumption = parseFloat(this.couponForm.minConsumption);
          } else {
            this.couponForm.minConsumption = 0; // 如果为空，设为0表示无门槛
          }

          if (this.couponForm.validPeriodType === 1 && this.couponForm.validDays) {
            this.couponForm.validDays = parseInt(this.couponForm.validDays);
          }

          // 如果选择了"指定设备"且有已选择的设备，则更新deviceIds
          if (this.couponForm.useScope == 1 && this.selectedDevices.length > 0) {
            this.couponForm.deviceIds = this.selectedDevices.map(device => device.id);
          }
          if (this.dialogTitle === '新增裂变券') {
            try {
              const { code } = await createCoupon(this.couponForm)
              if (code === '00000') {
                this.$message.success('创建裂变券成功')
                this.fetchData()
              }
            } catch (error) {
              this.$message.error('创建裂变券失败')
            }
          } else {
            // 编辑裂变券
            try {
              const { code } = await updateCoupon({
                id: this.currentCoupon.id,
                ...this.couponForm
              })
              if (code === '00000') {
                this.$message.success('编辑裂变券成功')
                this.fetchData()
              }
            } catch (error) {
              this.$message.error('编辑裂变券失败')
            }
          }

          this.editDialogVisible = false
        } else {
          return false
        }
      })
    },

    // 检查是否可以编辑
    canEdit(row) {
      return row.claimed === 0 && row.used === 0
    },

    // 格式化日期
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}`
    },

    // 处理抽屉关闭前的回调
    handleDrawerClose(done) {
      this.$confirm('确认关闭？未保存的数据将会丢失')
        .then(() => {
          done()
        })
        .catch(() => { })
    },

    // 处理使用范围变化
    handleUseScopeChange(value) {
      if (value === 1) {
        this.fetchDeviceList()
      } else {
        this.selectedDevices = []
        this.couponForm.deviceIds = []
      }
    },

    // 显示设备选择弹窗
    showDeviceDialog() {
      this.deviceDialogVisible = true
    },

    // 获取设备列表数据
    async fetchDeviceList() {
      this.deviceLoading = true
      try {
        const { code, data } = await getDeviceList(this.deviceQueryForm)
        if (code === '00000') {
          this.deviceList = data.data
          this.deviceQueryForm.total = data.paginator.totalRecord

          // 数据加载完成后，设置已选择的设备
          this.$nextTick(() => {
            if (this.selectedDevices.length > 0 && this.$refs.deviceTable) {
              this.deviceList.forEach(row => {
                if (this.selectedDevices.some(device => device.id == row.id)) {
                  this.$refs.deviceTable.toggleRowSelection(row, true)
                }
              })
            }
          })
          this.deviceDialogVisible = true
        } else {
          this.$message.error('获取设备列表失败')
        }
      } catch (error) {
        console.error('获取设备列表失败', error)
        this.$message.error('获取设备列表失败')
      } finally {
        this.deviceLoading = false
      }
    },

    // 设备查询
    handleDeviceQuery() {
      this.deviceQueryForm.currentPage = 1
      this.fetchDeviceList()
    },

    // 设备分页
    handleDevicePageChange(page) {
      this.deviceQueryForm.currentPage = page
      this.fetchDeviceList()
    },

    // 设备每页条数变化
    handleDeviceSizeChange(size) {
      this.deviceQueryForm.pageSize = size
      this.deviceQueryForm.currentPage = 1
      this.fetchDeviceList()
    },

    // 处理设备选择变化
    handleDeviceSelectionChange(selection) {
      this.selectedDevices = selection
      this.couponForm.deviceIds = selection.map(device => device.id)
    },

    // 确认设备选择
    confirmDeviceSelection() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning('请至少选择一个设备')
        return
      }
      this.deviceDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.index-container {
  padding: 10px;
}

.dialog-footer {
  margin-top: 20px;
}

.el-dialog__body {
  padding: 20px;
}

.el-descriptions {
  margin-bottom: 20px;
}
</style>
